<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <title>风险评估可视化报告</title>
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/semantic.min.css') }}">
  <script src="{{ url_for('static', filename='js/jquery-3.5.1.js') }}"></script>
  <script src="{{ url_for('static', filename='js/semantic.min.js') }}"></script>
  <style>
    body{ margin:0; background:#f6f8fb; }
    .container{ width:92%; max-width:1200px; margin:22px auto; }
    .header{ display:flex; justify-content:space-between; align-items:center; margin-bottom:14px; }
    .kpis{ display:flex; gap:10px; }
    .kpi{ background:#fff; border-radius:12px; padding:10px 14px; box-shadow:0 4px 18px rgba(0,0,0,0.06); }
    .grid{ display:grid; grid-template-columns:repeat(auto-fill,minmax(320px,1fr)); gap:14px; }
    .card{ background:#fff; border-radius:12px; padding:14px; box-shadow:0 4px 18px rgba(0,0,0,0.06); }
    .pill{ display:inline-block; padding:2px 8px; border-radius:999px; font-size:12px; margin-right:6px; }
    .pill.high{ background:#fee2e2; color:#b91c1c; }
    .pill.mid{ background:#ffedd5; color:#c2410c; }
    .pill.low{ background:#dcfce7; color:#166534; }
    .trend{ font-size:12px; color:#6b7280; }
    .chips{ display:flex; flex-wrap:wrap; gap:6px; }
    .chip{ background:#eef2ff; color:#3730a3; border-radius:999px; padding:2px 8px; font-size:12px; }
    .title{ font-weight:600; font-size:16px; margin-bottom:6px; }
    .section-title{ font-weight:700; margin:18px 0 10px; }
    .divider{ height:1px; background:#e5e7eb; margin:10px 0; }
  </style>
</head>
<body>
  <div class="container">
    <div id="header" class="header">
      <div>
        <div id="reportTitle" style="font-size:20px;font-weight:700;">风险评估可视化报告</div>
        <div id="reportMeta" style="font-size:12px;color:#6b7280;">-</div>
      </div>
      <div class="kpis">
        <div class="kpi"><div style="font-size:12px;color:#6b7280;">高风险</div><div id="kHigh" style="font-weight:700;font-size:18px;">-</div></div>
        <div class="kpi"><div style="font-size:12px;color:#6b7280;">中风险</div><div id="kMid" style="font-weight:700;font-size:18px;">-</div></div>
        <div class="kpi"><div style="font-size:12px;color:#6b7280;">低风险</div><div id="kLow" style="font-weight:700;font-size:18px;">-</div></div>
      </div>
    </div>

    <div class="card">
      <div class="section-title">总体摘要</div>
      <div id="summaryZh">-</div>
      <div class="divider"></div>
      <div id="highlights" class="chips"></div>
    </div>

    <div class="section-title">风险项</div>
    <div id="riskGrid" class="grid"></div>

    <div class="card">
      <div class="section-title">讨论</div>
      <div id="discussion">-</div>
    </div>
  </div>

  <script>
    function text(el, v){ el.textContent = v || '—'; }
    function pill(level){
      if(level === '高') return '<span class="pill high">高</span>';
      if(level === '中') return '<span class="pill mid">中</span>';
      if(level === '低') return '<span class="pill low">低</span>';
      return '';
    }

    function render(){
      const raw = sessionStorage.getItem('vizReportPayload');
      if(!raw){ return alert('未找到报告数据'); }
      let payload = {};
      try{ payload = JSON.parse(raw); }catch(_){ return alert('数据格式错误'); }
      const mode = payload.format;
      if(mode === 'text'){
        document.body.innerHTML = '<pre style="white-space:pre-wrap;padding:16px;">' + (payload.raw || '') + '</pre>';
        return;
      }
      const data = payload.data || {};
      const meta = data.metadata || data.report_meta || {};
      const overall = data.summary || data.overall_assessment || {};
      const viz = data.visualization || data.ui_hints || {};
      const risks = data.risks || [];

      // 头部
      text(document.getElementById('reportTitle'), meta.report_title || '风险评估可视化报告');
      text(document.getElementById('reportMeta'), `${meta.period_month || ''} ｜ ${meta.source_url || ''}`);
      const rcb = (viz.risk_count_by_level) || {};
      text(document.getElementById('kHigh'), rcb['高'] || 0);
      text(document.getElementById('kMid'), rcb['中'] || 0);
      text(document.getElementById('kLow'), rcb['低'] || 0);

      // 摘要与高光
      text(document.getElementById('summaryZh'), overall.zh || overall.summary || '—');
      const hl = viz.highlight_risks || overall.top_highlights || [];
      const hlWrap = document.getElementById('highlights');
      hlWrap.innerHTML = '';
      hl.forEach(x=>{ const span = document.createElement('span'); span.className='chip'; span.textContent=x; hlWrap.appendChild(span); });

      // 风险网格
      const grid = document.getElementById('riskGrid');
      grid.innerHTML = '';
      risks.forEach(r=>{
        const div = document.createElement('div');
        div.className = 'card';
        const att = r.category || r.attention_level || '';
        const lvl = r.risk_level || '—';
        const trd = r.trend || '—';
        const reg = (r.regions && (r.regions.class_regions || r.regions_impacted)) || [];
        const pops = r.populations_at_risk || [];
        const drivers = r.drivers || r.key_points || [];
        const recs = r.recommendations || [];

        const regionsHtml = Array.isArray(reg)
          ? reg.map(x=>`<span class="chip">${x}</span>`).join('')
          : (()=>{
              const c1 = ((r.regions||{}).class_regions||{}).class_I || [];
              const c2 = ((r.regions||{}).class_regions||{}).class_II || [];
              const p = (r.regions||{}).provinces || [];
              let arr = [];
              if(c1.length) arr = arr.concat(c1.map(x=>`Ⅰ类-${x}`));
              if(c2.length) arr = arr.concat(c2.map(x=>`Ⅱ类-${x}`));
              if(p.length) arr = arr.concat(p);
              return arr.map(x=>`<span class="chip">${x}</span>`).join('');
            })();

        const recsHtml = recs.slice(0,5).map(x=>{
          const t = typeof x === 'string' ? x : (x.text || '');
          return `<li>${t}</li>`;
        }).join('');

        div.innerHTML = `
          <div class="title">${r.name || r.topic || '—'}</div>
          <div style="margin-bottom:6px;">
            <span class="pill">${att}</span>
            ${pill(lvl)}
            <span class="trend">趋势：${trd}</span>
          </div>
          <div class="divider"></div>
          <div style="font-size:12px;color:#6b7280;margin-bottom:6px;">重点人群/驱动因素</div>
          <div class="chips">${drivers.slice(0,6).map(x=>`<span class="chip">${x}</span>`).join('')}</div>
          <div class="divider"></div>
          <div style="font-size:12px;color:#6b7280;margin-bottom:6px;">涉及地区</div>
          <div class="chips">${regionsHtml || '—'}</div>
          <div class="divider"></div>
          <div style="font-size:12px;color:#6b7280;margin-bottom:6px;">建议</div>
          <ul style="margin:0;padding-left:18px;">${recsHtml || '<li>—</li>'}</ul>
        `;
        grid.appendChild(div);
      });

      // 讨论
      const disc = data.discussion || '';
      text(document.getElementById('discussion'), disc || '—');
    }

    document.addEventListener('DOMContentLoaded', render);
  </script>
</body>
</html>

