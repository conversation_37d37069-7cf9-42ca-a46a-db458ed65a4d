import json
from flask import request, jsonify
from openai import OpenAI
from routes.call_chatgpt_url import load_api_config
from routes.get_news import fetch_content


PROMPT_TEMPLATE = (
    "你是一名公共卫生风控分析助手，负责将中国疾控中心发布的月度《突发公共卫生事件风险评估》PDF文本，归纳为结构化JSON，用于前端标准化可视化展示。\n"
    "严格遵守以下输出规范：\n"
    "1) 仅输出JSON，不要任何解释或前后缀；\n"
    "2) 字段名固定且必须包含：metadata, summary, risks, non_infectious, global, visualization;\n"
    "3) 字段含义与取值要求：\n"
    "- metadata: {report_title: string, period_month: string(YYYY-MM), source_url: string, language: 'zh'|'en'|'bilingual'}\n"
    "- summary: {zh: string(200-400字简要摘要), en: string(optional, 若原文含英文摘要则提取)}\n"
    "- risks: 数组，元素为重点传染病与一般关注传染病，结构为：{\n"
    "    name: string, category: '重点关注'|'一般关注', risk_level: '高'|'中'|'低',\n"
    "    trend: '上升'|'下降'|'波动'|'稳定', regions_impacted: [string],\n"
    "    transmission: [string], key_points: [string(3-6条)], recommendations: [string(2-6条)]\n"
    "  }\n"
    "- non_infectious: 数组，记录如食物中毒、高温中暑、洪涝灾害等：{\n"
    "    topic: string, risk_level: '高'|'中'|'低', trend: '上升'|'下降'|'波动'|'稳定',\n"
    "    drivers: [string], key_points: [string], recommendations: [string]\n"
    "  }\n"
    "- global: 数组，若报告提到全球态势则提炼，否则输出空数组，元素结构与risks相同或简化：{name, summary, risk_level}\n"
    "- visualization: {\n"
    "    highlight_risks: [string(3-8项最需关注的风险名称，按紧急程度排序)],\n"
    "    risk_count_by_level: {高: int, 中: int, 低: int},\n"
    "    tags: [string(如'蚊媒','肠道','人传人','高温','洪涝')],\n"
    "    notes: [string(重要提示/免责声明等)]\n"
    "  }\n"
    "4) 从PDF原文中准确提取中文术语（如“Ⅰ类地区”“布雷图指数”等），避免杜撰。\n"
    "5) 若无法确定某字段，合理留空或给出最接近判断，但不要发明数据。\n"
    "6) 输出必须是合法JSON。\n"
)


def _strip_code_fences(text: str) -> str:
    if not text:
        return text
    t = text.strip()
    if t.startswith("```"):
        # remove the first line and optional language hint
        lines = t.splitlines()[1:]
        # if the last line is ``` fence, drop it
        if lines and lines[-1].strip().startswith("```"):
            lines = lines[:-1]
        return "\n".join(lines).strip()
    return text


def handler():
    try:
        data = request.get_json(silent=True) or {}
        pdf_url = data.get("pdfUrl")
        title = data.get("title") or ""
        date = data.get("date") or ""
        if not pdf_url:
            return jsonify({"ok": False, "error": "缺少 pdfUrl"}), 400

        # 使用 Jina Reader 抽取 PDF 文本
        extracted_text = fetch_content(pdf_url)
        if not extracted_text or (
            isinstance(extracted_text, str)
            and extracted_text.startswith("Failed to fetch")
        ):
            return jsonify(
                {"ok": False, "error": "无法提取PDF文本，请检查URL是否可访问"}
            ), 502

        # 组织用户消息，包含辅助元信息
        user_payload = {
            "meta": {"title": title, "date": date, "source_pdf": pdf_url},
            "pdf_text": extracted_text[:200000],  # 限长，避免超长输入
        }
        user_message = json.dumps(user_payload, ensure_ascii=False)

        # 调用 OpenAI（与 call_chatgpt_url 风格一致），模型设为 gpt-5-mini
        cfg = load_api_config()
        client = OpenAI(
            base_url=cfg.get("api_endpoint", ""), api_key=cfg.get("api_key", "")
        )
        resp = client.chat.completions.create(
            model="gpt-5-mini",
            messages=[
                {"role": "system", "content": PROMPT_TEMPLATE},
                {"role": "user", "content": user_message},
            ],
            temperature=0.2,
        )
        content = resp.choices[0].message.content
        content = _strip_code_fences(content)

        # 解析为JSON
        try:
            parsed = json.loads(content)
        except Exception:
            # 返回原始文本，前端可以做降级展示
            return jsonify(
                {
                    "ok": True,
                    "raw": content,
                    "format": "text",
                    "rawPdfText": extracted_text,
                }
            )

        return jsonify(
            {"ok": True, "data": parsed, "format": "json", "rawPdfText": extracted_text}
        )

    except Exception as e:
        return jsonify({"ok": False, "error": f"服务器处理异常: {str(e)}"}), 500
