<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <title>重点传染病与突发公卫事件风险评估报告</title>
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/semantic.min.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/ag-grid.min.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/ag-theme-quartz.min.css') }}">
  <script src="{{ url_for('static', filename='js/jquery-3.5.1.js') }}"></script>
  <script src="{{ url_for('static', filename='js/semantic.min.js') }}"></script>
  <script src="{{ url_for('static', filename='js/ag-grid-community.min.noStyle.js') }}"></script>
  <script src="{{ url_for('static', filename='js/ag-grid-locale.zh.js') }}"></script>
  <style>
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
      overflow: hidden;
    }
    .main-container {
      width: 90%;
      height: 90%;
      margin: 2% auto;
      display: flex;
      flex-direction: column;
    }
    .header-container {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 20px;
    }
    .grid-container {
      flex: 1;
      width: 100%;
      min-height: 500px;
    }
    #myGrid {
      height: 100% !important;
      width: 100% !important;
    }
  </style>
</head>
<body>
  <div class="main-container">
    <div class="header-container">
      <h2 class="ui header" style="margin: 0 12px 0 0;">
        <i class="hospital icon"></i>
        <div class="content">
          重点传染病与突发公卫事件风险评估报告
          <div class="sub header">数据来自中国疾控中心官网实时获取</div>
        </div>
      </h2>
    </div>

    <div class="grid-container">
      <div id="myGrid" class="ag-theme-quartz" style="width:100%; height:100%;"></div>
    </div>
  </div>

  <script>
    const columnDefs = [
      { headerName: "序号", valueGetter: "node.rowIndex + 1", width: 80, filter: false, suppressSizeToFit: true,
        cellStyle: { 'text-align': 'center', 'display': 'flex', 'align-items': 'center', 'justify-content': 'center' } },
      { headerName: "标题", field: "title", flex: 1, minWidth: 500, autoHeight: true,
        cellStyle: { 'text-align': 'left', 'white-space': 'normal', 'line-height': '20px', 'display': 'flex', 'align-items': 'center', 'padding': '10px' } },
      { headerName: "发布日期", field: "date", width: 140,
        cellStyle: { 'text-align': 'center', 'display': 'flex', 'align-items': 'center', 'justify-content': 'center' } },
      { headerName: "查看原文", field: "pdfUrl", width: 110, suppressSizeToFit: true, cellRenderer: function(params){
          if (!params.value) return '';
          return '<div style="display: flex; align-items: center; justify-content: center; height: 100%;"><a href="' + params.value + '" target="_blank" class="ui basic blue button mini" style="margin: 0;">查看原文</a></div>';
        }, cellStyle: { 'padding': '5px' }
      },
      { headerName: "操作", width: 180, suppressSizeToFit: true, cellRenderer: function(params){
          const d = params && params.data ? params.data : {};
          const pdf = d.pdfUrl ? d.pdfUrl : '';
          const title = (d.title || '').replace(/"/g, '&quot;');
          const date = d.date || '';
          return '<div style="display: flex; align-items: center; justify-content: center; height: 100%;">'
            + '<button class="ui primary basic button mini gen-report-btn" style="margin: 0;"'
            + ' data-pdf="' + pdf + '" data-title="' + title + '" data-date="' + date + '">生成可视化报告</button>'
            + '</div>';
        }
      },
    ];

    const gridOptions = {
      columnDefs,
      pagination: true,
      paginationPageSize: 20,
      paginationPageSizeSelector: [10, 20, 50],
      localeText: AG_GRID_LOCALE_ZH,
      defaultColDef: { resizable: true, filter: true, sortable: true },
    };

    async function loadAllPages(){
      // 自动翻页聚合：先取首页，再从第二页开始直到无数据
      let all = [];
      let page = 1;
      while(true){
        let url = '/api/cdc_risk_reports_china';
        if(page > 1) url += `?page=${page}`;
        const data = await fetch(url).then(r=>r.json()).catch(()=>[]);
        if(!Array.isArray(data) || data.length === 0) break;
        all = all.concat(data);
        page += 1;
        // 简单上限，避免意外无限循环
        if(page > 50) break;
      }
      // 去重（按标题+日期）
      const key = x => `${x.title}__${x.date}`;
      const map = new Map();
      for(const r of all){ if(!map.has(key(r))) map.set(key(r), r); }
      const merged = Array.from(map.values());
      // 按日期倒序
      merged.sort((a,b)=> (a.date<b.date?1:-1));
      gridApi.setGridOption('rowData', merged);

      // 全屏遮罩层
      const overlay = document.createElement('div');
      overlay.id = 'reportOverlay';
      overlay.style.cssText = `position:fixed;inset:0;background:rgba(17,24,39,0.6);backdrop-filter:blur(3px);display:none;align-items:center;justify-content:center;z-index:9999;`;
      overlay.innerHTML = `
        <div style="background:#fff;border-radius:14px;box-shadow:0 10px 40px rgba(0,0,0,0.2);padding:28px 32px;max-width:520px;width:92%;text-align:center;">
          <div class="ui active inverted dimmer" style="position:relative;display:block;background:transparent;margin-bottom:14px;">
            <div class="ui text loader" style="font-size:1.05em;">加载中</div>
          </div>
          <div style="font-size:16px;color:#111827;margin-top:6px;">正在调用AI分析，请耐心等待，请勿刷新或关闭页面</div>
        </div>`;
      document.body.appendChild(overlay);

      function showOverlay(){ overlay.style.display = 'flex'; }
      function hideOverlay(){ overlay.style.display = 'none'; }

      // 行内按钮事件绑定（使用事件委托）
      document.addEventListener('click', async (e) => {
        const btn = e.target.closest('.gen-report-btn');
        if(!btn) return;
        const pdfUrl = btn.getAttribute('data-pdf');
        const title = btn.getAttribute('data-title');
        const date = btn.getAttribute('data-date');
        if(!pdfUrl){
          return alert('未找到PDF地址');
        }
        try{
          showOverlay();
          const res = await fetch('/api/generate_visualization_report', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ pdfUrl, title, date })
          });
          const data = await res.json();
          hideOverlay();
          if(!data.ok){
            return alert(data.error || '生成失败');
          }
          // 遮罩消失后再显示模态窗口
          showReportModal(data);
        }catch(err){
          hideOverlay();
          console.error(err);
          alert('生成失败，请稍后重试');
        }
      // 显示报告模态窗口
      function showReportModal(data) {
        // 创建模态窗口
        const modal = document.createElement('div');
        modal.className = 'ui fullscreen modal';
        modal.id = 'reportModal';
        modal.innerHTML = `
          <i class="close icon"></i>
          <div class="header">
            <i class="chart line icon"></i>
            风险评估可视化报告
          </div>
          <div class="scrolling content" id="reportContent">
            <div class="ui active centered inline loader">加载中...</div>
          </div>
          <div class="actions">
            <div class="ui black deny button">
              <i class="close icon"></i>
              关闭
            </div>
          </div>
        `;
        document.body.appendChild(modal);

        // 初始化并显示模态窗口
        $(modal).modal({
          closable: true,
          onHidden: function() {
            document.body.removeChild(modal);
          }
        }).modal('show');

        // 渲染报告内容
        setTimeout(() => renderReportContent(data), 100);
      }

      // 渲染报告内容
      function renderReportContent(payload) {
        const contentDiv = document.getElementById('reportContent');
        if (!contentDiv) return;

        const mode = payload.format;
        if (mode === 'text') {
          contentDiv.innerHTML = '<pre style="white-space:pre-wrap;padding:16px;font-size:13px;">' + (payload.raw || '') + '</pre>';
          return;
        }

        const data = payload.data || {};
        const meta = data.metadata || {};
        const overall = data.summary || {};
        const viz = data.visualization || {};
        const risks = data.risks || [];
        const rawPdfText = payload.rawPdfText || '';

        // 从原始PDF文本中提取讨论部分
        const discussionText = extractDiscussionFromPdf(rawPdfText);

        const html = `
          <style>
            .report-container { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; }
            .report-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding: 16px; background: #f8f9fa; border-radius: 8px; }
            .report-kpis { display: flex; gap: 12px; }
            .report-kpi { background: #fff; border-radius: 8px; padding: 8px 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); text-align: center; }
            .report-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 16px; margin: 20px 0; }
            .report-card { background: #fff; border-radius: 8px; padding: 16px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
            .report-pill { display: inline-block; padding: 2px 8px; border-radius: 12px; font-size: 11px; margin-right: 6px; }
            .report-pill.high { background: #fee2e2; color: #b91c1c; }
            .report-pill.mid { background: #ffedd5; color: #c2410c; }
            .report-pill.low { background: #dcfce7; color: #166534; }
            .report-chips { display: flex; flex-wrap: wrap; gap: 4px; margin: 8px 0; }
            .report-chip { background: #eef2ff; color: #3730a3; border-radius: 12px; padding: 2px 8px; font-size: 11px; }
            .report-title { font-weight: 600; font-size: 15px; margin-bottom: 8px; }
            .report-section-title { font-weight: 700; margin: 20px 0 12px; font-size: 16px; }
            .report-divider { height: 1px; background: #e5e7eb; margin: 12px 0; }
          </style>
          <div class="report-container">
            <div class="report-header">
              <div>
                <div style="font-size: 18px; font-weight: 700;">${meta.report_title || '风险评估可视化报告'}</div>
                <div style="font-size: 12px; color: #6b7280;">${meta.period_month || ''} | ${meta.source_url || ''}</div>
              </div>
              <div class="report-kpis">
                <div class="report-kpi">
                  <div style="font-size: 11px; color: #6b7280;">高风险</div>
                  <div style="font-weight: 700; font-size: 16px;">${(viz.risk_count_by_level && viz.risk_count_by_level['高']) || 0}</div>
                </div>
                <div class="report-kpi">
                  <div style="font-size: 11px; color: #6b7280;">中风险</div>
                  <div style="font-weight: 700; font-size: 16px;">${(viz.risk_count_by_level && viz.risk_count_by_level['中']) || 0}</div>
                </div>
                <div class="report-kpi">
                  <div style="font-size: 11px; color: #6b7280;">低风险</div>
                  <div style="font-weight: 700; font-size: 16px;">${(viz.risk_count_by_level && viz.risk_count_by_level['低']) || 0}</div>
                </div>
              </div>
            </div>

            <div class="report-card">
              <div class="report-section-title">总体摘要</div>
              <div>${overall.zh || overall.summary || '—'}</div>
              <div class="report-divider"></div>
              <div class="report-chips">
                ${(viz.highlight_risks || []).map(x => `<span class="report-chip">${x}</span>`).join('')}
              </div>
            </div>

            <div class="report-section-title">风险项</div>
            <div class="report-grid">
              ${risks.map(r => {
                const att = r.category || '';
                const lvl = r.risk_level || '—';
                const trd = r.trend || '—';
                const drivers = r.drivers || r.key_points || [];
                const recs = r.recommendations || [];
                const regions = r.regions_impacted || [];

                const pillClass = lvl === '高' ? 'high' : (lvl === '中' ? 'mid' : 'low');

                return `
                  <div class="report-card">
                    <div class="report-title">${r.name || r.topic || '—'}</div>
                    <div style="margin-bottom: 8px;">
                      <span class="report-pill">${att}</span>
                      <span class="report-pill ${pillClass}">${lvl}</span>
                      <span style="font-size: 11px; color: #6b7280;">趋势：${trd}</span>
                    </div>
                    <div class="report-divider"></div>
                    <div style="font-size: 11px; color: #6b7280; margin-bottom: 6px;">关键要点/驱动因素</div>
                    <div class="report-chips">
                      ${drivers.slice(0, 6).map(x => `<span class="report-chip">${x}</span>`).join('')}
                    </div>
                    <div class="report-divider"></div>
                    <div style="font-size: 11px; color: #6b7280; margin-bottom: 6px;">涉及地区</div>
                    <div class="report-chips">
                      ${regions.slice(0, 8).map(x => `<span class="report-chip">${x}</span>`).join('')}
                    </div>
                    <div class="report-divider"></div>
                    <div style="font-size: 11px; color: #6b7280; margin-bottom: 6px;">建议措施</div>
                    <ul style="margin: 0; padding-left: 16px; font-size: 12px;">
                      ${recs.slice(0, 4).map(x => {
                        const text = typeof x === 'string' ? x : (x.text || '');
                        return `<li style="margin-bottom: 4px;">${text}</li>`;
                      }).join('')}
                    </ul>
                  </div>
                `;
              }).join('')}
            </div>

            <div class="report-card">
              <div class="report-section-title">讨论</div>
              <div style="line-height: 1.6; font-size: 13px;">${discussionText || '—'}</div>
            </div>
          </div>
        `;

        contentDiv.innerHTML = html;
      }

      // 从PDF原文中提取讨论部分
      function extractDiscussionFromPdf(pdfText) {
        if (!pdfText) return '';

        // 查找"讨论"或"3 讨论"等标题
        const discussionMatch = pdfText.match(/(?:^|\n)\s*(?:3\s*)?讨论\s*\n([\s\S]*?)(?=\n\s*(?:\d+\s*)?(?:结论|参考文献|致谢|$))/i);
        if (discussionMatch && discussionMatch[1]) {
          return discussionMatch[1].trim().replace(/\n\s*\n/g, '\n\n');
        }

        // 如果没找到标准的讨论部分，尝试查找结论性段落
        const conclusionMatch = pdfText.match(/(?:根据|经|通过).*?(?:分析|评估|讨论).*?(?:主要结论|结论)[:：]?\s*([\s\S]{100,800}?)(?=\n\s*(?:\d+|参考文献|致谢|$))/i);
        if (conclusionMatch && conclusionMatch[1]) {
          return conclusionMatch[1].trim();
        }

        return '';
      }
      });

      setTimeout(()=> gridApi.sizeColumnsToFit(), 150);
    }

    let gridApi;
    document.addEventListener('DOMContentLoaded', () => {
      gridApi = agGrid.createGrid(document.getElementById('myGrid'), gridOptions);
      loadAllPages();
    });
  </script>
</body>
</html>

