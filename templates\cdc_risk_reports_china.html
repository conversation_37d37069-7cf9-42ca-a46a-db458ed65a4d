<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <title>重点传染病与突发公卫事件风险评估报告</title>
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/semantic.min.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/ag-grid.min.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/ag-theme-quartz.min.css') }}">
  <script src="{{ url_for('static', filename='js/jquery-3.5.1.js') }}"></script>
  <script src="{{ url_for('static', filename='js/semantic.min.js') }}"></script>
  <script src="{{ url_for('static', filename='js/ag-grid-community.min.noStyle.js') }}"></script>
  <script src="{{ url_for('static', filename='js/ag-grid-locale.zh.js') }}"></script>
  <style>
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
      overflow: hidden;
    }
    .main-container {
      width: 90%;
      height: 90%;
      margin: 2% auto;
      display: flex;
      flex-direction: column;
    }
    .header-container {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 20px;
    }
    .grid-container {
      flex: 1;
      width: 100%;
      min-height: 500px;
    }
    #myGrid {
      height: 100% !important;
      width: 100% !important;
    }
  </style>
</head>
<body>
  <div class="main-container">
    <div class="header-container">
      <h2 class="ui header" style="margin: 0 12px 0 0;">
        <i class="hospital icon"></i>
        <div class="content">
          重点传染病与突发公卫事件风险评估报告
          <div class="sub header">数据来自中国疾控中心官网实时获取</div>
        </div>
      </h2>
    </div>

    <div class="grid-container">
      <div id="myGrid" class="ag-theme-quartz" style="width:100%; height:100%;"></div>
    </div>
  </div>

  <script>
    const columnDefs = [
      { headerName: "序号", valueGetter: "node.rowIndex + 1", width: 80, filter: false, suppressSizeToFit: true,
        cellStyle: { 'text-align': 'center', 'display': 'flex', 'align-items': 'center', 'justify-content': 'center' } },
      { headerName: "标题", field: "title", flex: 1, minWidth: 500, autoHeight: true,
        cellStyle: { 'text-align': 'left', 'white-space': 'normal', 'line-height': '20px', 'display': 'flex', 'align-items': 'center', 'padding': '10px' } },
      { headerName: "发布日期", field: "date", width: 140,
        cellStyle: { 'text-align': 'center', 'display': 'flex', 'align-items': 'center', 'justify-content': 'center' } },
      { headerName: "查看原文", field: "pdfUrl", width: 110, suppressSizeToFit: true, cellRenderer: function(params){
          if (!params.value) return '';
          return '<div style="display: flex; align-items: center; justify-content: center; height: 100%;"><a href="' + params.value + '" target="_blank" class="ui basic blue button mini" style="margin: 0;">查看原文</a></div>';
        }, cellStyle: { 'padding': '5px' }
      },
      { headerName: "操作", width: 180, suppressSizeToFit: true, cellRenderer: function(params){
          const d = params && params.data ? params.data : {};
          const pdf = d.pdfUrl ? d.pdfUrl : '';
          const title = (d.title || '').replace(/"/g, '&quot;');
          const date = d.date || '';
          return '<div style="display: flex; align-items: center; justify-content: center; height: 100%;">'
            + '<button class="ui primary basic button mini gen-report-btn" style="margin: 0;"'
            + ' data-pdf="' + pdf + '" data-title="' + title + '" data-date="' + date + '">生成可视化报告</button>'
            + '</div>';
        }
      },
    ];

    const gridOptions = {
      columnDefs,
      pagination: true,
      paginationPageSize: 20,
      paginationPageSizeSelector: [10, 20, 50],
      localeText: AG_GRID_LOCALE_ZH,
      defaultColDef: { resizable: true, filter: true, sortable: true },
    };

    async function loadAllPages(){
      // 自动翻页聚合：先取首页，再从第二页开始直到无数据
      let all = [];
      let page = 1;
      while(true){
        let url = '/api/cdc_risk_reports_china';
        if(page > 1) url += `?page=${page}`;
        const data = await fetch(url).then(r=>r.json()).catch(()=>[]);
        if(!Array.isArray(data) || data.length === 0) break;
        all = all.concat(data);
        page += 1;
        // 简单上限，避免意外无限循环
        if(page > 50) break;
      }
      // 去重（按标题+日期）
      const key = x => `${x.title}__${x.date}`;
      const map = new Map();
      for(const r of all){ if(!map.has(key(r))) map.set(key(r), r); }
      const merged = Array.from(map.values());
      // 按日期倒序
      merged.sort((a,b)=> (a.date<b.date?1:-1));
      gridApi.setGridOption('rowData', merged);

      // 全屏遮罩层
      const overlay = document.createElement('div');
      overlay.id = 'reportOverlay';
      overlay.style.cssText = `position:fixed;inset:0;background:rgba(17,24,39,0.6);backdrop-filter:blur(3px);display:none;align-items:center;justify-content:center;z-index:9999;`;
      overlay.innerHTML = `
        <div style="background:#fff;border-radius:14px;box-shadow:0 10px 40px rgba(0,0,0,0.2);padding:28px 32px;max-width:520px;width:92%;text-align:center;">
          <div class="ui active inverted dimmer" style="position:relative;display:block;background:transparent;margin-bottom:14px;">
            <div class="ui text loader" style="font-size:1.05em;">加载中</div>
          </div>
          <div style="font-size:16px;color:#111827;margin-top:6px;">正在调用AI分析，请耐心等待，请勿刷新或关闭页面</div>
        </div>`;
      document.body.appendChild(overlay);

      function showOverlay(){ overlay.style.display = 'flex'; }
      function hideOverlay(){ overlay.style.display = 'none'; }

      // 行内按钮事件绑定（使用事件委托）
      document.addEventListener('click', async (e) => {
        const btn = e.target.closest('.gen-report-btn');
        if(!btn) return;
        const pdfUrl = btn.getAttribute('data-pdf');
        const title = btn.getAttribute('data-title');
        const date = btn.getAttribute('data-date');
        if(!pdfUrl){
          return alert('未找到PDF地址');
        }
        try{
          showOverlay();
          const res = await fetch('/api/generate_visualization_report', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ pdfUrl, title, date })
          });
          const data = await res.json();
          hideOverlay();
          if(!data.ok){
            return alert(data.error || '生成失败');
          }
          // 将结果存入 sessionStorage，跳转到展示页
          const payload = JSON.stringify(data);
          sessionStorage.setItem('vizReportPayload', payload);
          window.open('/viz_report_view', '_blank');
        }catch(err){
          hideOverlay();
          console.error(err);
          alert('生成失败，请稍后重试');
        }
      });

      setTimeout(()=> gridApi.sizeColumnsToFit(), 150);
    }

    let gridApi;
    document.addEventListener('DOMContentLoaded', () => {
      gridApi = agGrid.createGrid(document.getElementById('myGrid'), gridOptions);
      loadAllPages();
    });
  </script>
</body>
</html>

